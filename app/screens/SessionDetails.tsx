import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Session, sessionService } from '@/services/sessionService';
import { useLocalSearchParams } from 'expo-router';

export default function SessionDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchSession = async () => {
        try {
          setLoading(true);
          setError(null);
          const data = await sessionService.getSession(id as string);

          console.log('==========================================>>>')
          console.log(data.user, data)
          setSession(data);
        } catch (err) {
          setError('خطا در دریافت جزئیات جلسه.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchSession();
    } else {
      setError('شناسه جلسه ارائه نشده است.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری جزئیات جلسه...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!session) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>جلسه یافت نشد.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>جزئیات جلسه</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>شناسه:</ThemedText>
        <ThemedText>{session.id}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>ایمیل کاربر:</ThemedText>
        <ThemedText>{session.user.email}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>نام آزمون:</ThemedText>
        <ThemedText>{session.exam.name}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>زمان شروع:</ThemedText>
        <ThemedText>{new Date(session.start_time).toLocaleString()}</ThemedText>
      </ThemedView>
      {session.end_time && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>زمان پایان:</ThemedText>
          <ThemedText>{new Date(session.end_time).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>وضعیت:</ThemedText>
        <ThemedText>{session.status}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>امتیاز:</ThemedText>
        <ThemedText>{session.score}</ThemedText>
      </ThemedView>
      {session.created_at && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>تاریخ ایجاد:</ThemedText>
          <ThemedText>{new Date(session.created_at).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
      {session.updated_at && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>تاریخ بروزرسانی:</ThemedText>
          <ThemedText>{new Date(session.updated_at).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
