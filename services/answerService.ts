import { api } from './api';

// Define types
export interface Answer {
  id: string;
  sessionId: number;
  questionId: number;
  selectedOptionId: number;
  is_correct: boolean;
  createdAt?: string;
}

export interface CreateAnswerDto {
  sessionId: number;
  questionId: number;
  selectedOptionId: number;
  is_correct: boolean;
}

// Answer service for handling answer-related API calls
export const answerService = {
  // Submit an answer to a question in a session
  async submitAnswer(answerData: CreateAnswerDto): Promise<Answer> {
    const response = await api.post<Answer>('/v1/api/answers', answerData);
    return response.data;
  },

  // Get all answers
  async getAllAnswers(): Promise<Answer[]> {
    const response = await api.get<Answer[]>('/v1/api/answers');
    return response.data;
  },

  // Get an answer by ID
  async getAnswerById(id: string): Promise<Answer> {
    const response = await api.get<Answer>(`/v1/api/answers/${id}`);
    return response.data;
  }
};
